import { IDocumentInfo } from '../interfaces/document';
import { DocumentType, RunningStatus } from '../constants/knowledge';
import { Badge, DescriptionsProps, Flex, Popover, Space, Tag } from 'antd';
import reactStringReplace from 'react-string-replace';
import { isParserRunning } from './utils';
import { formatDate } from '@/utils/date';

interface IProps {
  record: IDocumentInfo;
}

const RunningStatusMap = {
  [RunningStatus.UNSTART]: {
    label: '待处理',
    color: 'orange',
  },
  [RunningStatus.RUNNING]: {
    label: '解析中',
    color: 'blue',
  },
  [RunningStatus.CANCEL]: { label: '取消', color: 'orange' },
  [RunningStatus.DONE]: { label: '已处理', color: 'green' },
  [RunningStatus.FAIL]: { label: '失败', color: 'red' },
};

const PopoverContent = ({ record }: IProps) => {
  const replaceText = (text: string) => {
    // Remove duplicate \n
    const nextText = text.replace(/(\n)\1+/g, '$1');

    const replacedText = reactStringReplace(
      nextText,
      /(\[ERROR\].+\s)/g,
      (match, i) => {
        return (
          <span key={i} className="text-[red]">
            {match}
          </span>
        );
      }
    );

    return replacedText;
  };

  const items: DescriptionsProps['items'] = [
    {
      key: 'embed_process_begin_at',
      label: '开始于',
      children: formatDate(
        record.embed_process_begin_at,
        'YYYY-MM-DD HH:mm:ss'
      ),
    },
    {
      key: 'embed_process_duation',
      label: '持续时间',
      children: `${(record.embed_process_duation || 0).toFixed(2)} s`,
    },
    {
      key: 'embed_progress_msg',
      label: '进度',
      children: replaceText(
        record.embed_progress_msg ? record.embed_progress_msg.trim() : ''
      ),
    },
  ];

  return (
    <Flex vertical className="w-[300px]">
      {items.map((x, idx) => {
        return (
          <div key={x.key} className={idx < 2 ? 'flex gap-[10px]' : ''}>
            <b>{x.label}:</b>
            <div
              className="whitespace-pre-line max-h-[200px] overflow-auto"
              style={{ scrollbarWidth: 'thin' }}
            >
              {x.children}
            </div>
          </div>
        );
      })}
    </Flex>
  );
};

const ParsingStatusCell = ({ record }: IProps) => {
  const text = record.embed_run || RunningStatus.UNSTART;
  const runningStatus = RunningStatusMap[text];
  const label = runningStatus.label;

  const isRunning = isParserRunning(text);

  const progress = record.embed_progress || 0;

  return record.type === DocumentType.Virtual ? null : (
    <Flex justify={'end'} align="center">
      <Popover content={<PopoverContent record={record}></PopoverContent>}>
        <Tag className="mr-0" color={runningStatus.color}>
          {isRunning ? (
            <Space>
              <Badge color={runningStatus.color} />
              {label}
              <span>{(progress * 100).toFixed(2)}%</span>
            </Space>
          ) : (
            label
          )}
        </Tag>
      </Popover>
    </Flex>
  );
};

export default ParsingStatusCell;
